import React from 'react'
import Image from 'next/image'

const Company = () => {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="py-16 bg-white">
        <div className="max-w-[1400px] mx-auto px-6">
          <div className="grid lg:grid-cols-2 gap-12 items-start mb-12">
            <div className="space-y-6">
              <div className="flex items-center gap-2">
                <Image
                  src="/icons/cubeicon.png"
                  alt="Cube Icon"
                  width={20}
                  height={20}
                  className="w-5 h-5"
                />
                <p className="text-[#454545] font-medium text-sm tracking-wider uppercase">WHO WE ARE</p>
              </div>
              <h1 className="font-manrope font-semibold text-[40px] leading-[120%] tracking-[0%] text-woodsmoke-950">
                Our Innovative Solutions for Your Delivery Services.
              </h1>
            </div>
            <div className="space-y-6">
              <p className="text-lg text-woodsmoke-600 leading-relaxed">
                Koolboks Logistics offers a range of services designed to meet diverse needs. Whether for homes, businesses, our services ensure reliable cooling without reliance on traditional energy sources.
              </p>
              <button className="bg-primary-500 hover:bg-primary-400 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                Our Services
              </button>
            </div>
          </div>

          {/* Full Width Globe Image */}
          <div className="w-full">
            <div className="w-full h-[400px] relative flex items-center justify-center">
              <Image
                src="/images/globe.png"
                alt="Global Network"
                width={1200}
                height={400}
                className="w-full h-full object-contain"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="py-20 bg-white">
        <div className="max-w-[1400px] mx-auto px-6 grid lg:grid-cols-2 gap-16 items-center">
          <div className="relative">
            <div className="w-[576px] h-[416px] rounded-[20px] overflow-hidden">
              <Image
                src="/images/team.png"
                alt="Our Team"
                width={576}
                height={416}
                className="w-full h-full object-cover"
              />
            </div>
          </div>
          <div className="space-y-8">
            <h2 className="font-manrope font-semibold text-[40px] leading-[120%] tracking-[0%] text-woodsmoke-950">
              The values that drive<br />everything we do.
            </h2>
            <div className="flex gap-8 relative">
              <button className="pb-4 px-2 text-primary-500 font-semibold transition-colors relative">
                Our Mission
                <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-primary-500"></div>
              </button>
              <button className="pb-4 px-2 text-woodsmoke-600 font-semibold hover:text-woodsmoke-950 transition-colors">
                Our Values
              </button>
            </div>
            <div className="space-y-6">
              <h3 className="text-2xl font-bold text-woodsmoke-950">
                Redefining Logistics for a Faster, Smarter World
              </h3>
              <p className="text-lg text-woodsmoke-600 leading-relaxed">
               For athletes, high altitude produces two contradictory effects on performance.<br /> For explosive events Physiological respiration involves the mechanisms that ensure that the composition of the functional. The long barrow was built on land previously inhabited in the Mesolithic period. It consisted of a Physical space is often conceived in three linear dimensions, although modern physicists
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Statistics Section */}
      <section className="py-6 bg-white">
        <div className="max-w-[1200px] mx-auto px-6">
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8 text-center">
            <div className="space-y-3">
              <div className="text-6xl font-bold text-primary-500">1M+</div>
              <p className="text-woodsmoke-600 font-medium">Shipments Delivered</p>
            </div>
            <div className="space-y-3">
              <div className="text-6xl font-bold text-primary-500">99%</div>
              <p className="text-woodsmoke-600 font-medium">On-time delivery Rate</p>
            </div>
            <div className="space-y-3">
              <div className="text-6xl font-bold text-primary-500">25</div>
              <p className="text-woodsmoke-600 font-medium">Delivery Locations</p>
            </div>
            <div className="space-y-3">
              <div className="text-6xl font-bold text-primary-500">8+</div>
              <p className="text-woodsmoke-600 font-medium">Delivery Partners</p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20">
        <div className="max-w-[1400px] mx-auto px-6">
          <div className="bg-[#F4F0E6] rounded-2xl overflow-hidden grid lg:grid-cols-2 items-stretch">
            <div className="relative">
              <div className="w-full h-full min-h-[350px]">
                <Image
                  src="/images/delivery.png"
                  alt="Delivery Service"
                  width={500}
                  height={350}
                  className="w-full h-full object-cover rounded-l-2xl"
                />
              </div>
            </div>
            <div className="p-8 lg:p-12 flex flex-col justify-center">
              <div className="space-y-6">
                <p className="text-[#666666] font-medium text-sm tracking-wide uppercase">HIRE US FOR KOOL DELIVERY</p>
                <h2 className="text-3xl lg:text-4xl font-bold text-[#2D2D2D] leading-tight">
                  Looking for the best logistics transport service?
                </h2>
                <div className="pt-4 space-y-4">
                  <button className="bg-[#FF6B35] hover:bg-[#E55A2B] text-white px-6 py-3 rounded-lg font-semibold text-sm transition-colors">
                    Request Quote
                  </button>
                  <div className="flex items-center gap-2 text-[#666666]">
                    <Image
                      src="/icons/PhoneCall.svg"
                      alt="Phone"
                      width={16}
                      height={16}
                      className="w-4 h-4"
                    />
                    <span className="font-medium text-sm">081 **********</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-woodsmoke-950 text-white py-16">
        <div className="max-w-[1400px] mx-auto px-6">
          <div className="grid lg:grid-cols-4 gap-12">
            {/* Company Info */}
            <div className="space-y-6">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-primary-500 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-lg">K</span>
                </div>
                <span className="text-xl font-bold">KOOL LOGISTICS</span>
              </div>
              <div className="flex space-x-4">
                <a href="#" className="w-10 h-10 bg-woodsmoke-800 rounded-lg flex items-center justify-center hover:bg-primary-500 transition-colors">
                  <span className="text-sm">📘</span>
                </a>
                <a href="#" className="w-10 h-10 bg-woodsmoke-800 rounded-lg flex items-center justify-center hover:bg-primary-500 transition-colors">
                  <span className="text-sm">📷</span>
                </a>
                <a href="#" className="w-10 h-10 bg-woodsmoke-800 rounded-lg flex items-center justify-center hover:bg-primary-500 transition-colors">
                  <span className="text-sm">💼</span>
                </a>
                <a href="#" className="w-10 h-10 bg-woodsmoke-800 rounded-lg flex items-center justify-center hover:bg-primary-500 transition-colors">
                  <span className="text-sm">🐦</span>
                </a>
                <a href="#" className="w-10 h-10 bg-woodsmoke-800 rounded-lg flex items-center justify-center hover:bg-primary-500 transition-colors">
                  <span className="text-sm">📺</span>
                </a>
              </div>
            </div>

            {/* Say Hello */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Say Hello</h3>
              <div className="space-y-3 text-woodsmoke-300">
                <p><EMAIL></p>
                <p>+1 800 123 45 67</p>
              </div>
            </div>

            {/* Useful Link */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Useful Link</h3>
              <div className="space-y-3 text-woodsmoke-300">
                <a href="#" className="block hover:text-primary-500 transition-colors">About us</a>
                <a href="#" className="block hover:text-primary-500 transition-colors">Pricing</a>
                <a href="#" className="block hover:text-primary-500 transition-colors">Guide</a>
                <a href="#" className="block hover:text-primary-500 transition-colors">Contact</a>
              </div>
            </div>

            {/* Our Services */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Our Services</h3>
              <div className="space-y-3 text-woodsmoke-300">
                <a href="#" className="block hover:text-primary-500 transition-colors">Logistics</a>
                <a href="#" className="block hover:text-primary-500 transition-colors">Manufacturing</a>
                <a href="#" className="block hover:text-primary-500 transition-colors">Production</a>
                <a href="#" className="block hover:text-primary-500 transition-colors">Automotive</a>
              </div>
            </div>
          </div>

          {/* Bottom Footer */}
          <div className="border-t border-woodsmoke-800 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center gap-4">
            <p className="text-woodsmoke-400 text-sm">
              Copyright © 2024 Kool Logistics. All rights reserved.
            </p>
            <div className="flex gap-6 text-sm text-woodsmoke-400">
              <a href="#" className="hover:text-primary-500 transition-colors">Terms of Service</a>
              <a href="#" className="hover:text-primary-500 transition-colors">Privacy Policy</a>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}

export default Company

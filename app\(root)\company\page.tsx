import React from 'react'
import Image from 'next/image'

const Company = () => {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="py-16 xl:py-24 2xl:py-32 bg-white">
        <div className="max-w-7xl xl:max-w-8xl 2xl:max-w-9xl mx-auto px-6 xl:px-8 2xl:px-12">
          <div className="grid lg:grid-cols-2 xl:grid-cols-5 gap-12 xl:gap-16 2xl:gap-20 items-start mb-12 xl:mb-16 2xl:mb-20">
            <div className="space-y-6 xl:space-y-8 2xl:space-y-10 xl:col-span-3">
              <div className="flex items-center gap-2 xl:gap-3">
                <Image
                  src="/icons/cubeicon.png"
                  alt="Cube Icon"
                  width={20}
                  height={20}
                  className="w-5 h-5 xl:w-6 xl:h-6 2xl:w-7 2xl:h-7"
                />
                <p className="text-[#454545] font-medium text-sm xl:text-base 2xl:text-lg tracking-wider uppercase">WHO WE ARE</p>
              </div>
              <h1 className="font-manrope font-semibold text-[40px] xl:text-[56px] 2xl:text-[72px] leading-[120%] tracking-[0%] text-woodsmoke-950">
                Our Innovative Solutions for Your Delivery Services.
              </h1>
            </div>
            <div className="space-y-6 xl:space-y-8 2xl:space-y-10 xl:col-span-2">
              <p className="text-lg xl:text-xl 2xl:text-2xl text-woodsmoke-600 leading-relaxed xl:leading-relaxed">
                Koolboks Logistics offers a range of services designed to meet diverse needs. Whether for homes, businesses, our services ensure reliable cooling without reliance on traditional energy sources.
              </p>
              <button className="bg-primary-500 hover:bg-primary-400 text-white px-6 py-3 xl:px-8 xl:py-4 2xl:px-10 2xl:py-5 rounded-lg font-medium xl:text-lg 2xl:text-xl transition-colors">
                Our Services
              </button>
            </div>
          </div>

          {/* Full Width Globe Image */}
          <div className="w-full">
            <div className="w-full h-[400px] xl:h-[500px] 2xl:h-[600px] relative flex items-center justify-center">
              <Image
                src="/images/globe.png"
                alt="Global Network"
                width={1200}
                height={400}
                className="w-full h-full object-contain"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="py-20 xl:py-28 2xl:py-36 bg-white">
        <div className="max-w-7xl xl:max-w-8xl 2xl:max-w-9xl mx-auto px-6 xl:px-8 2xl:px-12 grid lg:grid-cols-2 xl:grid-cols-5 gap-16 xl:gap-20 2xl:gap-24 items-center">
          <div className="relative xl:col-span-2">
            <div className="w-full h-[400px] xl:h-[500px] 2xl:h-[600px] rounded-2xl overflow-hidden">
              <Image
                src="/images/team.png"
                alt="Our Team"
                width={600}
                height={400}
                className="w-full h-full object-cover"
              />
            </div>
          </div>
          <div className="space-y-8 xl:space-y-10 2xl:space-y-12 xl:col-span-3">
            <h2 className="text-4xl lg:text-5xl xl:text-6xl 2xl:text-7xl font-bold text-woodsmoke-950 leading-tight">
              The values that drive everything we do.
            </h2>
            <div className="flex gap-8 xl:gap-12 border-b border-gray-200">
              <button className="pb-4 px-2 text-primary-500 font-semibold xl:text-lg 2xl:text-xl border-b-2 border-primary-500 transition-colors">
                Our Mission
              </button>
              <button className="pb-4 px-2 text-woodsmoke-600 font-semibold xl:text-lg 2xl:text-xl hover:text-woodsmoke-950 transition-colors">
                Our Values
              </button>
            </div>
            <div className="space-y-6 xl:space-y-8 2xl:space-y-10">
              <h3 className="text-2xl xl:text-3xl 2xl:text-4xl font-bold text-woodsmoke-950">
                Redefining Logistics for a Faster, Smarter World
              </h3>
              <p className="text-lg xl:text-xl 2xl:text-2xl text-woodsmoke-600 leading-relaxed xl:leading-relaxed">
               For athletes, high altitude produces two contradictory effects on performance. For explosive events Physiological respiration involves the mechanisms that ensure that the composition of the functional. The long barrow was built on land previously inhabited in the Mesolithic period. It consisted of a Physical space is often conceived in three linear dimensions, although modern physicists
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Statistics Section */}
      <section className="py-6 xl:py-8 2xl:py-12 bg-white">
        <div className="max-w-4xl xl:max-w-6xl 2xl:max-w-7xl mx-auto px-6 xl:px-8 2xl:px-12">
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8 xl:gap-12 2xl:gap-16 text-center">
            <div className="space-y-3 xl:space-y-4 2xl:space-y-6">
              <div className="text-6xl xl:text-7xl 2xl:text-8xl font-bold text-primary-500">1M+</div>
              <p className="text-woodsmoke-600 font-medium xl:text-lg 2xl:text-xl">Shipments Delivered</p>
            </div>
            <div className="space-y-3 xl:space-y-4 2xl:space-y-6">
              <div className="text-6xl xl:text-7xl 2xl:text-8xl font-bold text-primary-500">99%</div>
              <p className="text-woodsmoke-600 font-medium xl:text-lg 2xl:text-xl">On-time delivery Rate</p>
            </div>
            <div className="space-y-3 xl:space-y-4 2xl:space-y-6">
              <div className="text-6xl xl:text-7xl 2xl:text-8xl font-bold text-primary-500">25</div>
              <p className="text-woodsmoke-600 font-medium xl:text-lg 2xl:text-xl">Delivery Locations</p>
            </div>
            <div className="space-y-3 xl:space-y-4 2xl:space-y-6">
              <div className="text-6xl xl:text-7xl 2xl:text-8xl font-bold text-primary-500">8+</div>
              <p className="text-woodsmoke-600 font-medium xl:text-lg 2xl:text-xl">Delivery Partners</p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 xl:py-28 2xl:py-36">
        <div className="max-w-7xl xl:max-w-8xl 2xl:max-w-9xl mx-auto px-6 xl:px-8 2xl:px-12">
          <div className="bg-[#F4F0E6] rounded-2xl xl:rounded-3xl 2xl:rounded-4xl overflow-hidden grid lg:grid-cols-2 xl:grid-cols-5 items-stretch">
            <div className="relative xl:col-span-2">
              <div className="w-full h-full min-h-[350px] xl:min-h-[450px] 2xl:min-h-[550px]">
                <Image
                  src="/images/delivery.png"
                  alt="Delivery Service"
                  width={500}
                  height={350}
                  className="w-full h-full object-cover rounded-l-2xl xl:rounded-l-3xl 2xl:rounded-l-4xl"
                />
              </div>
            </div>
            <div className="p-8 lg:p-12 xl:p-16 2xl:p-20 flex flex-col justify-center xl:col-span-3">
              <div className="space-y-6 xl:space-y-8 2xl:space-y-10">
                <p className="text-[#666666] font-medium text-sm xl:text-base 2xl:text-lg tracking-wide uppercase">HIRE US FOR KOOL DELIVERY</p>
                <h2 className="text-3xl lg:text-4xl xl:text-5xl 2xl:text-6xl font-bold text-[#2D2D2D] leading-tight">
                  Looking for the best logistics transport service?
                </h2>
                <div className="pt-4 xl:pt-6 2xl:pt-8 space-y-4 xl:space-y-6 2xl:space-y-8">
                  <button className="bg-[#FF6B35] hover:bg-[#E55A2B] text-white px-6 py-3 xl:px-8 xl:py-4 2xl:px-10 2xl:py-5 rounded-lg font-semibold text-sm xl:text-base 2xl:text-lg transition-colors">
                    Request Quote
                  </button>
                  <div className="flex items-center gap-2 xl:gap-3 text-[#666666]">
                    <Image
                      src="/icons/PhoneCall.svg"
                      alt="Phone"
                      width={16}
                      height={16}
                      className="w-4 h-4 xl:w-5 xl:h-5 2xl:w-6 2xl:h-6"
                    />
                    <span className="font-medium text-sm xl:text-base 2xl:text-lg">081 **********</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-woodsmoke-950 text-white py-16 xl:py-20 2xl:py-24">
        <div className="max-w-7xl xl:max-w-8xl 2xl:max-w-9xl mx-auto px-6 xl:px-8 2xl:px-12">
          <div className="grid lg:grid-cols-4 xl:grid-cols-6 gap-12 xl:gap-16 2xl:gap-20">
            {/* Company Info */}
            <div className="space-y-6 xl:space-y-8 2xl:space-y-10 xl:col-span-2">
              <div className="flex items-center gap-3 xl:gap-4">
                <div className="w-10 h-10 xl:w-12 xl:h-12 2xl:w-14 2xl:h-14 bg-primary-500 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-lg xl:text-xl 2xl:text-2xl">K</span>
                </div>
                <span className="text-xl xl:text-2xl 2xl:text-3xl font-bold">KOOL LOGISTICS</span>
              </div>
              <div className="flex space-x-4 xl:space-x-6">
                <a href="#" className="w-10 h-10 xl:w-12 xl:h-12 2xl:w-14 2xl:h-14 bg-woodsmoke-800 rounded-lg flex items-center justify-center hover:bg-primary-500 transition-colors">
                  <span className="text-sm xl:text-base 2xl:text-lg">📘</span>
                </a>
                <a href="#" className="w-10 h-10 xl:w-12 xl:h-12 2xl:w-14 2xl:h-14 bg-woodsmoke-800 rounded-lg flex items-center justify-center hover:bg-primary-500 transition-colors">
                  <span className="text-sm xl:text-base 2xl:text-lg">📷</span>
                </a>
                <a href="#" className="w-10 h-10 xl:w-12 xl:h-12 2xl:w-14 2xl:h-14 bg-woodsmoke-800 rounded-lg flex items-center justify-center hover:bg-primary-500 transition-colors">
                  <span className="text-sm xl:text-base 2xl:text-lg">💼</span>
                </a>
                <a href="#" className="w-10 h-10 xl:w-12 xl:h-12 2xl:w-14 2xl:h-14 bg-woodsmoke-800 rounded-lg flex items-center justify-center hover:bg-primary-500 transition-colors">
                  <span className="text-sm xl:text-base 2xl:text-lg">🐦</span>
                </a>
                <a href="#" className="w-10 h-10 xl:w-12 xl:h-12 2xl:w-14 2xl:h-14 bg-woodsmoke-800 rounded-lg flex items-center justify-center hover:bg-primary-500 transition-colors">
                  <span className="text-sm xl:text-base 2xl:text-lg">📺</span>
                </a>
              </div>
            </div>

            {/* Say Hello */}
            <div className="space-y-4 xl:space-y-6 2xl:space-y-8">
              <h3 className="text-lg xl:text-xl 2xl:text-2xl font-semibold">Say Hello</h3>
              <div className="space-y-3 xl:space-y-4 2xl:space-y-5 text-woodsmoke-300 xl:text-lg 2xl:text-xl">
                <p><EMAIL></p>
                <p>+1 800 123 45 67</p>
              </div>
            </div>

            {/* Useful Link */}
            <div className="space-y-4 xl:space-y-6 2xl:space-y-8">
              <h3 className="text-lg xl:text-xl 2xl:text-2xl font-semibold">Useful Link</h3>
              <div className="space-y-3 xl:space-y-4 2xl:space-y-5 text-woodsmoke-300 xl:text-lg 2xl:text-xl">
                <a href="#" className="block hover:text-primary-500 transition-colors">About us</a>
                <a href="#" className="block hover:text-primary-500 transition-colors">Pricing</a>
                <a href="#" className="block hover:text-primary-500 transition-colors">Guide</a>
                <a href="#" className="block hover:text-primary-500 transition-colors">Contact</a>
              </div>
            </div>

            {/* Our Services */}
            <div className="space-y-4 xl:space-y-6 2xl:space-y-8">
              <h3 className="text-lg xl:text-xl 2xl:text-2xl font-semibold">Our Services</h3>
              <div className="space-y-3 xl:space-y-4 2xl:space-y-5 text-woodsmoke-300 xl:text-lg 2xl:text-xl">
                <a href="#" className="block hover:text-primary-500 transition-colors">Logistics</a>
                <a href="#" className="block hover:text-primary-500 transition-colors">Manufacturing</a>
                <a href="#" className="block hover:text-primary-500 transition-colors">Production</a>
                <a href="#" className="block hover:text-primary-500 transition-colors">Automotive</a>
              </div>
            </div>
          </div>

          {/* Bottom Footer */}
          <div className="border-t border-woodsmoke-800 mt-12 xl:mt-16 2xl:mt-20 pt-8 xl:pt-10 2xl:pt-12 flex flex-col md:flex-row justify-between items-center gap-4 xl:gap-6 2xl:gap-8">
            <p className="text-woodsmoke-400 text-sm xl:text-base 2xl:text-lg">
              Copyright © 2024 Kool Logistics. All rights reserved.
            </p>
            <div className="flex gap-6 xl:gap-8 2xl:gap-10 text-sm xl:text-base 2xl:text-lg text-woodsmoke-400">
              <a href="#" className="hover:text-primary-500 transition-colors">Terms of Service</a>
              <a href="#" className="hover:text-primary-500 transition-colors">Privacy Policy</a>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}

export default Company

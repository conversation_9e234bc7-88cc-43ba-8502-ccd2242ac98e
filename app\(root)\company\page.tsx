import React from 'react'
import Image from 'next/image'

const Company = () => {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-orange-50 to-white">
        <div className="max-w-7xl mx-auto px-6 grid lg:grid-cols-2 gap-16 items-center">
          <div className="space-y-8">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-primary-500 rounded-full"></div>
              <p className="text-primary-500 font-medium text-sm tracking-wider uppercase">WHO WE ARE</p>
            </div>
            <h1 className="text-5xl lg:text-6xl font-bold text-woodsmoke-950 leading-tight">
              Our Innovative Solutions for Your <span className="text-primary-500">Delivery Services.</span>
            </h1>
            <p className="text-lg text-woodsmoke-600 leading-relaxed max-w-lg">
              Koolbox Logistics offers a range of services designed to meet diverse needs. Whether for homes, businesses, our services ensure convenience as your trusted partner in energy solutions.
            </p>
            <button className="bg-primary-500 hover:bg-primary-400 text-white px-8 py-4 rounded-xl font-semibold text-lg transition-colors">
              Get Started
            </button>
          </div>

        </div>
      </section>

      {/* Values Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-6 grid lg:grid-cols-2 gap-16 items-center">
          <div className="relative">
            <div className="w-full h-[400px] rounded-2xl overflow-hidden">
              {/* Team meeting image - using a placeholder that represents the team meeting from the design */}
              <div className="w-full h-full bg-gradient-to-br from-orange-100 to-orange-50 flex items-center justify-center relative">
                <div className="absolute inset-0 bg-gradient-to-br from-transparent to-black/10"></div>
                {/* Simulating team meeting scene */}
                <div className="relative z-10 flex items-center justify-center space-x-4">
                  <div className="w-16 h-16 bg-primary-500 rounded-full flex items-center justify-center">
                    <span className="text-white font-bold text-xl">👥</span>
                  </div>
                  <div className="text-center">
                    <div className="text-woodsmoke-950 font-semibold text-lg">Team Collaboration</div>
                    <div className="text-woodsmoke-600 text-sm">Working Together</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="space-y-8">
            <h2 className="text-4xl lg:text-5xl font-bold text-woodsmoke-950 leading-tight">
              The values that drive everything we do.
            </h2>
            <div className="flex gap-8 border-b border-gray-200">
              <button className="pb-4 px-2 text-primary-500 font-semibold border-b-2 border-primary-500 transition-colors">
                Our Mission
              </button>
              <button className="pb-4 px-2 text-woodsmoke-600 font-semibold hover:text-woodsmoke-950 transition-colors">
                Our Values
              </button>
            </div>
            <div className="space-y-6">
              <h3 className="text-2xl font-bold text-woodsmoke-950">
                Redefining Logistics for a Faster, Smarter World
              </h3>
              <p className="text-lg text-woodsmoke-600 leading-relaxed">
                We are committed to revolutionizing the logistics industry through innovation and cutting-edge technology. Our mission is to provide seamless, efficient, and reliable logistics solutions that empower businesses to thrive in today's fast-paced world. We strive to be the trusted partner that connects people and places, ensuring that every delivery is a step towards a more connected and efficient future.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Statistics Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-6">
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-12 text-center">
            <div className="space-y-3">
              <div className="text-6xl font-bold text-primary-500">1M+</div>
              <p className="text-woodsmoke-600 font-medium">Shipments Delivered</p>
            </div>
            <div className="space-y-3">
              <div className="text-6xl font-bold text-primary-500">99%</div>
              <p className="text-woodsmoke-600 font-medium">On-time delivery Rate</p>
            </div>
            <div className="space-y-3">
              <div className="text-6xl font-bold text-primary-500">25</div>
              <p className="text-woodsmoke-600 font-medium">Delivery Locations</p>
            </div>
            <div className="space-y-3">
              <div className="text-6xl font-bold text-primary-500">8+</div>
              <p className="text-woodsmoke-600 font-medium">Delivery Partners</p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-orange-100 to-orange-50">
        <div className="max-w-7xl mx-auto px-6 grid lg:grid-cols-2 gap-16 items-center">
          <div className="relative">
            <div className="w-full h-[400px] rounded-2xl overflow-hidden">
              {/* Delivery person image - representing the delivery person from the design */}
              <div className="w-full h-full bg-gradient-to-br from-primary-400 to-primary-500 flex items-center justify-center relative">
                <div className="absolute inset-0 bg-gradient-to-br from-transparent to-black/10"></div>
                {/* Simulating delivery person with packages */}
                <div className="relative z-10 flex flex-col items-center justify-center space-y-4">
                  <div className="w-20 h-20 bg-white rounded-full flex items-center justify-center">
                    <span className="text-primary-500 font-bold text-2xl">📦</span>
                  </div>
                  <div className="text-center">
                    <div className="text-white font-semibold text-xl">Fast Delivery</div>
                    <div className="text-orange-100 text-sm">Professional Service</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="space-y-8">
            <div className="space-y-4">
              <p className="text-primary-500 font-semibold text-sm tracking-wider uppercase">HIRE US FOR POOL DELIVERY</p>
              <h2 className="text-4xl lg:text-5xl font-bold text-woodsmoke-950 leading-tight">
                Looking for the best logistics transport service?
              </h2>
            </div>
            <div className="flex flex-col sm:flex-row gap-6 items-start">
              <button className="bg-primary-500 hover:bg-primary-400 text-white px-8 py-4 rounded-xl font-semibold text-lg transition-colors">
                Request Quote
              </button>
              <div className="flex items-center gap-3 text-woodsmoke-700">
                <div className="w-10 h-10 bg-white rounded-full flex items-center justify-center shadow-sm">
                  <span className="text-primary-500 text-lg">📞</span>
                </div>
                <span className="font-semibold text-lg">081 364 70711</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-woodsmoke-950 text-white py-16">
        <div className="max-w-7xl mx-auto px-6">
          <div className="grid lg:grid-cols-4 gap-12">
            {/* Company Info */}
            <div className="space-y-6">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-primary-500 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-lg">K</span>
                </div>
                <span className="text-xl font-bold">KOOL LOGISTICS</span>
              </div>
              <div className="flex space-x-4">
                <a href="#" className="w-10 h-10 bg-woodsmoke-800 rounded-lg flex items-center justify-center hover:bg-primary-500 transition-colors">
                  <span className="text-sm">📘</span>
                </a>
                <a href="#" className="w-10 h-10 bg-woodsmoke-800 rounded-lg flex items-center justify-center hover:bg-primary-500 transition-colors">
                  <span className="text-sm">📷</span>
                </a>
                <a href="#" className="w-10 h-10 bg-woodsmoke-800 rounded-lg flex items-center justify-center hover:bg-primary-500 transition-colors">
                  <span className="text-sm">💼</span>
                </a>
                <a href="#" className="w-10 h-10 bg-woodsmoke-800 rounded-lg flex items-center justify-center hover:bg-primary-500 transition-colors">
                  <span className="text-sm">🐦</span>
                </a>
                <a href="#" className="w-10 h-10 bg-woodsmoke-800 rounded-lg flex items-center justify-center hover:bg-primary-500 transition-colors">
                  <span className="text-sm">📺</span>
                </a>
              </div>
            </div>

            {/* Say Hello */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Say Hello</h3>
              <div className="space-y-3 text-woodsmoke-300">
                <p><EMAIL></p>
                <p>+1 800 123 45 67</p>
              </div>
            </div>

            {/* Useful Link */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Useful Link</h3>
              <div className="space-y-3 text-woodsmoke-300">
                <a href="#" className="block hover:text-primary-500 transition-colors">About us</a>
                <a href="#" className="block hover:text-primary-500 transition-colors">Pricing</a>
                <a href="#" className="block hover:text-primary-500 transition-colors">Guide</a>
                <a href="#" className="block hover:text-primary-500 transition-colors">Contact</a>
              </div>
            </div>

            {/* Our Services */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Our Services</h3>
              <div className="space-y-3 text-woodsmoke-300">
                <a href="#" className="block hover:text-primary-500 transition-colors">Logistics</a>
                <a href="#" className="block hover:text-primary-500 transition-colors">Manufacturing</a>
                <a href="#" className="block hover:text-primary-500 transition-colors">Production</a>
                <a href="#" className="block hover:text-primary-500 transition-colors">Automotive</a>
              </div>
            </div>
          </div>

          {/* Bottom Footer */}
          <div className="border-t border-woodsmoke-800 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center gap-4">
            <p className="text-woodsmoke-400 text-sm">
              Copyright © 2024 Kool Logistics. All rights reserved.
            </p>
            <div className="flex gap-6 text-sm text-woodsmoke-400">
              <a href="#" className="hover:text-primary-500 transition-colors">Terms of Service</a>
              <a href="#" className="hover:text-primary-500 transition-colors">Privacy Policy</a>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}

export default Company

import React from 'react'
import Image from 'next/image'

const Company = () => {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="py-16 bg-gradient-to-br from-orange-50 to-white">
        <div className="max-w-6xl mx-auto px-4 grid md:grid-cols-2 gap-12 items-center">
          <div>
            <p className="text-primary-500 font-medium mb-4">WHO WE ARE</p>
            <h1 className="h1-semibold text-woodsmoke-950 mb-6">
              Our Innovative Solutions for Your Delivery Services.
            </h1>
            <p className="text-woodsmoke-600 mb-8 leading-relaxed">
              Koolbox Logistics offers a range of services designed to meet diverse needs. Whether for homes, businesses, our services ensure convenience as your trusted partner in energy solutions.
            </p>
            <button className="btn-primary">
              Get Started
            </button>
          </div>
          <div className="relative">
            <div className="w-full h-96 bg-gray-100 rounded-lg flex items-center justify-center">
              {/* World map illustration placeholder */}
              <span className="text-gray-400">World Map Illustration</span>
            </div>
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="py-16 bg-white">
        <div className="max-w-6xl mx-auto px-4 grid md:grid-cols-2 gap-12 items-center">
          <div className="relative">
            <div className="w-full h-80 bg-gray-100 rounded-lg flex items-center justify-center">
              {/* Team meeting image placeholder */}
              <span className="text-gray-400">Team Meeting Image</span>
            </div>
          </div>
          <div>
            <h2 className="text-4xl font-semibold text-woodsmoke-950 mb-6">
              The values that drive everything we do.
            </h2>
            <div className="flex gap-8 mb-6">
              <button className="btn-primary-no-bg border-b-2 border-primary-500">
                Our Mission
              </button>
              <button className="btn-secondary-no-bg">
                Our Values
              </button>
            </div>
            <h3 className="text-xl font-semibold text-woodsmoke-950 mb-4">
              Redefining Logistics for a Faster, Smarter World
            </h3>
            <p className="text-woodsmoke-600 leading-relaxed">
              We are committed to revolutionizing the logistics industry through innovation and cutting-edge technology. Our mission is to provide seamless, efficient, and reliable logistics solutions that empower businesses to thrive in today's fast-paced world. We strive to be the trusted partner that connects people and places, ensuring that every delivery is a step towards a more connected and efficient future.
            </p>
          </div>
        </div>
      </section>

      {/* Statistics Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-6xl mx-auto px-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
            <div>
              <div className="text-4xl font-bold text-primary-500 mb-2">1M+</div>
              <p className="text-woodsmoke-600">Shipments Delivered</p>
            </div>
            <div>
              <div className="text-4xl font-bold text-primary-500 mb-2">99%</div>
              <p className="text-woodsmoke-600">On-time delivery Rate</p>
            </div>
            <div>
              <div className="text-4xl font-bold text-primary-500 mb-2">25</div>
              <p className="text-woodsmoke-600">Delivery Locations</p>
            </div>
            <div>
              <div className="text-4xl font-bold text-primary-500 mb-2">8+</div>
              <p className="text-woodsmoke-600">Delivery Partners</p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-gradient-to-r from-orange-100 to-orange-50">
        <div className="max-w-6xl mx-auto px-4 grid md:grid-cols-2 gap-12 items-center">
          <div className="relative">
            <div className="w-full h-80 bg-gray-100 rounded-lg flex items-center justify-center">
              {/* Delivery person image placeholder */}
              <span className="text-gray-400">Delivery Person Image</span>
            </div>
          </div>
          <div>
            <p className="text-primary-500 font-medium mb-4">HIRE US FOR POOL DELIVERY</p>
            <h2 className="text-4xl font-semibold text-woodsmoke-950 mb-6">
              Looking for the best logistics transport service?
            </h2>
            <div className="flex flex-col sm:flex-row gap-4">
              <button className="btn-primary">
                Request Quote
              </button>
              <div className="flex items-center gap-2 text-woodsmoke-600">
                <span>📞</span>
                <span>081 364 70711</span>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

export default Company
